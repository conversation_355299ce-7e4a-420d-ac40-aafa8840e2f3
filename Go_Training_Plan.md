# Go Programming Training Plan for Automation and Integration Team

## Overview

This comprehensive 32-session training plan is designed for beginners in Go programming who will be working on automation and integration projects. Each session is structured for 1 hour with a balance of theory (45 minutes) and hands-on practice (15 minutes).

## Prerequisites

- Basic programming experience in any language
- Understanding of fundamental programming concepts (variables, functions, loops, etc.)
- Familiarity with command-line interfaces
- Development environment setup (Go 1.24+, VS Code or similar IDE)

## Course Structure

- **Total Sessions**: 32 sessions
- **Duration**: 1 hour per session
- **Format**: 45 minutes theory + 15 minutes hands-on practice
- **Target Audience**: Automation and Integration team members

---

## Table of Contents

### Phase 1: Foundation (Sessions 1-8)

1. [Go Environment Setup and Language Introduction](#session-1)
2. [Variables and Type System](#session-2)
3. [Struct Types and Memory Layout](#session-3)
4. [Pointers Part 1: Pass by Value and Sharing Data](#session-4)
5. [Pointers Part 2: Escape Analysis and Memory Management](#session-5)
6. [Pointers Part 3: Stack Growth and Garbage Collection](#session-6)
7. [Constants and Type Safety](#session-7)
8. [Data-Oriented Design Principles](#session-8)

### Phase 2: Data Structures (Sessions 9-14)

9. [Arrays: Mechanical Sympathy and Performance](#session-9)
10. [Arrays: Semantics and Value Types](#session-10)
11. [Slices Part 1: Declaration, Length, and Reference Types](#session-11)
12. [Slices Part 2: Appending and Memory Management](#session-12)
13. [Slices Part 3: Slicing Operations and References](#session-13)
14. [Strings, Maps, and Range Mechanics](#session-14)

### Phase 3: Object-Oriented Concepts (Sessions 15-20)

15. [Methods Part 1: Declaration and Receiver Behavior](#session-15)
16. [Methods Part 2: Value vs Pointer Semantics](#session-16)
17. [Methods Part 3: Function Variables and Method Sets](#session-17)
18. [Interfaces Part 1: Polymorphism and Design](#session-18)
19. [Interfaces Part 2: Method Sets and Storage](#session-19)
20. [Embedding, Exporting, and Composition Patterns](#session-20)

### Phase 4: Advanced Composition (Sessions 21-23)

21. [Grouping Types and Decoupling Strategies](#session-21)
22. [Interface Conversions, Assertions, and Design Guidelines](#session-22)
23. [Mocking and Testing Strategies](#session-23)

### Phase 5: Error Handling (Sessions 24-25)

24. [Error Handling: Values, Variables, and Context](#session-24)
25. [Advanced Error Handling: Wrapping and Debugging](#session-25)

### Phase 6: Code Organization (Sessions 26-27)

26. [Package Design and Language Mechanics](#session-26)
27. [Package-Oriented Design and Best Practices](#session-27)

### Phase 7: Concurrency Fundamentals (Sessions 28-30)

28. [Scheduler Mechanics and Goroutines](#session-28)
29. [Data Races and Synchronization](#session-29)
30. [Channels and Signaling Semantics](#session-30)

### Phase 8: Advanced Concurrency and Quality (Sessions 31-32)

31. [Advanced Channel Patterns and Context](#session-31)
32. [Testing, Benchmarking, and Performance Analysis](#session-32)

---

## Session Details

### Session 1: Go Environment Setup and Language Introduction

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Set up Go development environment with latest tooling (Go 1.24+)
- Understand Go's design philosophy and use cases in automation
- Learn basic Go toolchain commands and workspace management
- Create first Go program with modern best practices

**Videos Covered**:

- Course introduction and overview (conceptual)

**Key Concepts**:

- Go installation and GOPATH vs Go modules
- Go workspaces for multi-module projects
- Basic project structure and naming conventions
- Introduction to `go mod`, `go build`, `go run`
- VS Code setup with Go extension

**Hands-on Exercise**:

```go
// Create a simple automation utility
package main

import (
    "fmt"
    "os"
    "time"
)

func main() {
    fmt.Printf("System Monitor started at %v\n", time.Now())
    fmt.Printf("Current working directory: %s\n", getCurrentDir())
}

func getCurrentDir() string {
    dir, err := os.Getwd()
    if err != nil {
        return "unknown"
    }
    return dir
}
```

**Prerequisites**: None

---

### Session 2: Variables and Type System

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master Go's type system and its importance in automation
- Understand zero values and memory initialization
- Learn variable declaration patterns and when to use each
- Grasp the relationship between type, memory, and performance

**Videos Covered**:

- 2.1 Topics (0:00:48)
- 2.2 Variables (0:16:26)

**Key Concepts**:

- Type is life: size and representation
- Built-in types: numerics, strings, booleans
- Architecture-dependent types (int, uint, uintptr)
- Zero values and integrity
- var vs := declaration patterns
- String internal structure (2-word data structure)

**Hands-on Exercise**:

```go
// Configuration parser for automation tools
package main

import "fmt"

type Config struct {
    ServerPort    int
    DatabaseURL   string
    EnableLogging bool
    MaxRetries    int
}

func main() {
    // Practice different variable declaration patterns
    var defaultConfig Config // zero value initialization
    
    activeConfig := Config{
        ServerPort:    8080,
        DatabaseURL:   "localhost:5432",
        EnableLogging: true,
        MaxRetries:    3,
    }
    
    fmt.Printf("Default config: %+v\n", defaultConfig)
    fmt.Printf("Active config: %+v\n", activeConfig)
    
    // Demonstrate type information
    analyzeTypes()
}

func analyzeTypes() {
    var i int
    var s string
    var b bool
    
    fmt.Printf("int size: %d bytes, value: %d\n", unsafe.Sizeof(i), i)
    fmt.Printf("string size: %d bytes, value: %q\n", unsafe.Sizeof(s), s)
    fmt.Printf("bool size: %d bytes, value: %t\n", unsafe.Sizeof(b), b)
}
```

**Prerequisites**: Session 1

---

### Session 3: Struct Types and Memory Layout

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Design effective struct types for automation data
- Understand memory layout and alignment
- Learn struct initialization patterns
- Apply struct design to real-world automation scenarios

**Videos Covered**:

- 2.3 Struct Types (0:23:27)

**Key Concepts**:

- Struct declaration and initialization
- Memory layout and padding
- Anonymous structs and embedded fields
- Struct tags for JSON/XML processing
- Value vs reference semantics with structs

**Hands-on Exercise**:

```go
// API response structures for integration work
package main

import (
    "encoding/json"
    "fmt"
    "time"
)

type APIResponse struct {
    Status    string    `json:"status"`
    Message   string    `json:"message"`
    Data      []Item    `json:"data"`
    Timestamp time.Time `json:"timestamp"`
}

type Item struct {
    ID          int    `json:"id"`
    Name        string `json:"name"`
    Description string `json:"description,omitempty"`
}

func main() {
    // Create sample API response
    response := APIResponse{
        Status:    "success",
        Message:   "Data retrieved successfully",
        Timestamp: time.Now(),
        Data: []Item{
            {ID: 1, Name: "Server Monitor", Description: "Monitors server health"},
            {ID: 2, Name: "Log Aggregator"},
        },
    }
    
    // Convert to JSON (common in automation)
    jsonData, err := json.MarshalIndent(response, "", "  ")
    if err != nil {
        fmt.Printf("Error: %v\n", err)
        return
    }
    
    fmt.Printf("API Response:\n%s\n", jsonData)
}
```

**Prerequisites**: Session 2

---

### Session 4: Pointers Part 1: Pass by Value and Sharing Data

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand Go's pass-by-value semantics
- Learn when and why to use pointers for data sharing
- Master pointer syntax and dereferencing
- Apply pointer concepts to automation data structures

**Videos Covered**:

- 2.4 Pointers Part 1 (Pass by Value) (0:15:45)
- 2.5 Pointer Part 2 (Sharing Data) (0:10:35)

**Key Concepts**:

- Everything in Go is pass by value
- Pointer types and address operators (&, *)
- When to use value vs pointer semantics
- Cost of copying vs sharing
- Pointer safety and nil checks

**Hands-on Exercise**:

```go
// Configuration management with pointers
package main

import "fmt"

type DatabaseConfig struct {
    Host     string
    Port     int
    Username string
    Password string
}

// Value receiver - creates a copy
func (d DatabaseConfig) GetConnectionString() string {
    return fmt.Sprintf("%s:%s@%s:%d", d.Username, d.Password, d.Host, d.Port)
}

// Pointer receiver - works with original
func (d *DatabaseConfig) UpdatePassword(newPassword string) {
    d.Password = newPassword
}

func main() {
    config := DatabaseConfig{
        Host:     "localhost",
        Port:     5432,
        Username: "admin",
        Password: "oldpass",
    }

    fmt.Printf("Original config: %+v\n", config)

    // Demonstrate value semantics
    connectionStr := config.GetConnectionString()
    fmt.Printf("Connection string: %s\n", connectionStr)

    // Demonstrate pointer semantics
    config.UpdatePassword("newpass")
    fmt.Printf("Updated config: %+v\n", config)

    // Working with pointers directly
    configPtr := &config
    fmt.Printf("Config via pointer: %+v\n", *configPtr)
}
```

**Prerequisites**: Session 3

---

### Session 5: Pointers Part 2: Escape Analysis and Memory Management

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand escape analysis and its impact on performance
- Learn stack vs heap allocation decisions
- Master memory management concepts for automation applications
- Use Go tools to analyze memory allocation

**Videos Covered**:

- 2.6 Pointers Part 3 (Escape Analysis) (0:20:20)
- 2.7 Pointers Part 4 (Stack Growth) (0:07:32)

**Key Concepts**:

- Stack vs heap allocation
- Escape analysis rules and compiler decisions
- Performance implications of memory allocation
- Using `go build -gcflags="-m"` for escape analysis
- Memory efficiency in long-running automation processes

**Hands-on Exercise**:

```go
// Memory-efficient data processing for automation
package main

import (
    "fmt"
    "runtime"
)

type LogEntry struct {
    Timestamp string
    Level     string
    Message   string
    Source    string
}

// Stack allocation - doesn't escape
func processLogEntryValue(entry LogEntry) LogEntry {
    entry.Level = "PROCESSED_" + entry.Level
    return entry
}

// Heap allocation - escapes to heap
func processLogEntryPointer(entry *LogEntry) *LogEntry {
    processed := &LogEntry{
        Timestamp: entry.Timestamp,
        Level:     "PROCESSED_" + entry.Level,
        Message:   entry.Message,
        Source:    entry.Source,
    }
    return processed
}

func main() {
    // Monitor memory usage
    var m1, m2 runtime.MemStats
    runtime.GC()
    runtime.ReadMemStats(&m1)

    // Process many log entries
    for i := 0; i < 10000; i++ {
        entry := LogEntry{
            Timestamp: "2025-01-01T00:00:00Z",
            Level:     "INFO",
            Message:   fmt.Sprintf("Processing item %d", i),
            Source:    "automation-service",
        }

        // Use value semantics (stack allocation)
        _ = processLogEntryValue(entry)
    }

    runtime.GC()
    runtime.ReadMemStats(&m2)

    fmt.Printf("Memory allocated: %d bytes\n", m2.TotalAlloc-m1.TotalAlloc)
    fmt.Printf("Heap objects: %d\n", m2.HeapObjects)
}
```

**Prerequisites**: Session 4

---

### Session 6: Pointers Part 3: Stack Growth and Garbage Collection

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand Go's stack growth mechanism
- Learn garbage collection fundamentals and tuning
- Optimize memory usage for automation workloads
- Monitor and profile memory usage in Go applications

**Videos Covered**:

- 2.8 Pointers Part 5 (Garbage Collection) (0:15:13)

**Key Concepts**:

- Stack growth and segmented stacks
- Garbage collection phases and timing
- GC tuning with GOGC environment variable
- Memory profiling tools and techniques
- Best practices for memory-efficient automation

**Hands-on Exercise**:

```go
// GC-aware batch processing system
package main

import (
    "fmt"
    "runtime"
    "runtime/debug"
    "time"
)

type BatchProcessor struct {
    batchSize int
    processed int
}

func (bp *BatchProcessor) ProcessBatch(items []string) {
    for _, item := range items {
        // Simulate processing work
        _ = fmt.Sprintf("Processing: %s", item)
        bp.processed++
    }

    // Force GC periodically for demonstration
    if bp.processed%1000 == 0 {
        runtime.GC()
        debug.FreeOSMemory()
    }
}

func main() {
    // Configure GC for automation workload
    debug.SetGCPercent(50) // More aggressive GC

    processor := &BatchProcessor{batchSize: 100}

    // Simulate processing large batches
    start := time.Now()

    for batch := 0; batch < 50; batch++ {
        items := make([]string, processor.batchSize)
        for i := range items {
            items[i] = fmt.Sprintf("item_%d_%d", batch, i)
        }

        processor.ProcessBatch(items)

        // Monitor memory every 10 batches
        if batch%10 == 0 {
            var m runtime.MemStats
            runtime.ReadMemStats(&m)
            fmt.Printf("Batch %d: Heap=%dKB, GC=%d\n",
                batch, m.HeapAlloc/1024, m.NumGC)
        }
    }

    duration := time.Since(start)
    fmt.Printf("Processed %d items in %v\n", processor.processed, duration)
}
```

**Prerequisites**: Session 5

---

### Session 7: Constants and Type Safety

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master Go's constant system and type safety
- Understand iota and constant generation
- Learn best practices for configuration constants
- Apply constants effectively in automation systems

**Videos Covered**:

- 2.9 Constants (0:15:29)

**Key Concepts**:

- Typed vs untyped constants
- Constant expressions and compile-time evaluation
- iota for generating constant sequences
- Constants for configuration and enumeration
- Type safety with constants

**Hands-on Exercise**:

```go
// Configuration constants for automation system
package main

import "fmt"

// Service status constants using iota
type ServiceStatus int

const (
    StatusUnknown ServiceStatus = iota
    StatusStarting
    StatusRunning
    StatusStopping
    StatusStopped
    StatusError
)

// Configuration constants
const (
    DefaultTimeout = 30 // untyped constant
    MaxRetries     = 3
    BufferSize     = 1024
)

// String representation for status
func (s ServiceStatus) String() string {
    switch s {
    case StatusUnknown:
        return "Unknown"
    case StatusStarting:
        return "Starting"
    case StatusRunning:
        return "Running"
    case StatusStopping:
        return "Stopping"
    case StatusStopped:
        return "Stopped"
    case StatusError:
        return "Error"
    default:
        return "Invalid"
    }
}

type AutomationService struct {
    Name   string
    Status ServiceStatus
}

func main() {
    services := []AutomationService{
        {"Database Monitor", StatusRunning},
        {"Log Processor", StatusStarting},
        {"Alert Manager", StatusError},
    }

    fmt.Println("Service Status Report:")
    for _, service := range services {
        fmt.Printf("- %s: %s (%d)\n", service.Name, service.Status, service.Status)
    }

    // Demonstrate constant usage
    fmt.Printf("\nConfiguration:\n")
    fmt.Printf("- Default Timeout: %d seconds\n", DefaultTimeout)
    fmt.Printf("- Max Retries: %d\n", MaxRetries)
    fmt.Printf("- Buffer Size: %d bytes\n", BufferSize)
}
```

**Prerequisites**: Session 6

---

### Session 8: Data-Oriented Design Principles

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand data-oriented design philosophy
- Learn to optimize data structures for performance
- Apply mechanical sympathy principles
- Design efficient data layouts for automation workloads

**Videos Covered**:

- 3.1 Topics (0:00:41)
- 3.2 Data-Oriented Design (0:04:52)

**Key Concepts**:

- Data-oriented vs object-oriented design
- Cache-friendly data structures
- Memory layout optimization
- Mechanical sympathy principles
- Performance implications of data organization

**Hands-on Exercise**:

```go
// Efficient data structures for log processing
package main

import (
    "fmt"
    "time"
    "unsafe"
)

// Poor design - scattered data
type LogEntryPoor struct {
    ID        int64
    Timestamp time.Time
    Level     string
    Message   string
    Source    string
    Tags      map[string]string
}

// Better design - grouped by access patterns
type LogBatch struct {
    // Hot data - frequently accessed together
    IDs        []int64
    Timestamps []int64 // Unix timestamps for better cache locality
    Levels     []uint8 // Encoded levels (0=DEBUG, 1=INFO, etc.)

    // Cold data - less frequently accessed
    Messages []string
    Sources  []string
}

// Level encoding for better memory efficiency
const (
    LevelDebug uint8 = iota
    LevelInfo
    LevelWarn
    LevelError
    LevelFatal
)

func (lb *LogBatch) AddEntry(id int64, timestamp time.Time, level uint8, message, source string) {
    lb.IDs = append(lb.IDs, id)
    lb.Timestamps = append(lb.Timestamps, timestamp.Unix())
    lb.Levels = append(lb.Levels, level)
    lb.Messages = append(lb.Messages, message)
    lb.Sources = append(lb.Sources, source)
}

func (lb *LogBatch) CountByLevel(targetLevel uint8) int {
    count := 0
    // Cache-friendly iteration - only touching level data
    for _, level := range lb.Levels {
        if level == targetLevel {
            count++
        }
    }
    return count
}

func main() {
    // Compare memory usage
    fmt.Printf("LogEntryPoor size: %d bytes\n", unsafe.Sizeof(LogEntryPoor{}))

    // Create efficient log batch
    batch := &LogBatch{}

    // Add sample entries
    now := time.Now()
    for i := 0; i < 1000; i++ {
        batch.AddEntry(
            int64(i),
            now.Add(time.Duration(i)*time.Second),
            LevelInfo,
            fmt.Sprintf("Processing item %d", i),
            "automation-service",
        )
    }

    // Efficient processing - only touches needed data
    errorCount := batch.CountByLevel(LevelError)
    infoCount := batch.CountByLevel(LevelInfo)

    fmt.Printf("Processed %d entries\n", len(batch.IDs))
    fmt.Printf("Info entries: %d, Error entries: %d\n", infoCount, errorCount)
}
```

**Prerequisites**: Session 7

---

### Session 9: Arrays: Mechanical Sympathy and Performance

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand array fundamentals and memory layout
- Learn mechanical sympathy principles with arrays
- Master array performance characteristics
- Apply arrays effectively in automation scenarios

**Videos Covered**:

- 3.3 Arrays Part 1 (Mechanical Sympathy) (0:33:10)

**Key Concepts**:

- Array declaration and initialization
- Contiguous memory layout and cache performance
- Array vs slice performance trade-offs
- Fixed-size collections in automation
- Memory predictability and allocation

**Hands-on Exercise**:

```go
// High-performance metrics collection using arrays
package main

import (
    "fmt"
    "time"
)

const (
    MetricsBufferSize = 1000
    SampleCount      = 100
)

// Fixed-size metrics buffer for predictable performance
type MetricsCollector struct {
    cpuUsage    [MetricsBufferSize]float64
    memoryUsage [MetricsBufferSize]float64
    timestamps  [MetricsBufferSize]int64
    index       int
    full        bool
}

func (mc *MetricsCollector) AddMetric(cpu, memory float64) {
    mc.cpuUsage[mc.index] = cpu
    mc.memoryUsage[mc.index] = memory
    mc.timestamps[mc.index] = time.Now().Unix()

    mc.index++
    if mc.index >= MetricsBufferSize {
        mc.index = 0
        mc.full = true
    }
}

func (mc *MetricsCollector) GetAverages() (float64, float64) {
    count := mc.index
    if mc.full {
        count = MetricsBufferSize
    }

    if count == 0 {
        return 0, 0
    }

    var cpuSum, memSum float64
    for i := 0; i < count; i++ {
        cpuSum += mc.cpuUsage[i]
        memSum += mc.memoryUsage[i]
    }

    return cpuSum / float64(count), memSum / float64(count)
}

func main() {
    collector := &MetricsCollector{}

    // Simulate collecting metrics
    fmt.Println("Collecting system metrics...")
    for i := 0; i < SampleCount; i++ {
        // Simulate CPU and memory readings
        cpu := 20.0 + float64(i%50)
        memory := 60.0 + float64(i%30)

        collector.AddMetric(cpu, memory)

        if i%20 == 0 {
            avgCPU, avgMem := collector.GetAverages()
            fmt.Printf("Sample %d - Avg CPU: %.2f%%, Avg Memory: %.2f%%\n",
                i, avgCPU, avgMem)
        }
    }

    finalCPU, finalMem := collector.GetAverages()
    fmt.Printf("\nFinal averages - CPU: %.2f%%, Memory: %.2f%%\n",
        finalCPU, finalMem)
}
```

**Prerequisites**: Session 8

---

### Session 10: Arrays: Semantics and Value Types

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master array value semantics and copying behavior
- Understand when arrays are appropriate vs slices
- Learn array comparison and assignment rules
- Apply array semantics in automation contexts

**Videos Covered**:

- 3.4 Arrays Part 2 (Semantics) (0:16:43)

**Key Concepts**:

- Arrays are value types (copied on assignment)
- Array comparison and equality
- Passing arrays to functions (copy vs pointer)
- Array literals and initialization patterns
- When to choose arrays over slices

**Hands-on Exercise**:

```go
// Configuration validation using array semantics
package main

import "fmt"

const MaxConfigItems = 10

type ConfigValidator struct {
    requiredFields [MaxConfigItems]string
    fieldCount     int
}

func NewConfigValidator(fields []string) ConfigValidator {
    var validator ConfigValidator

    // Copy fields into fixed array
    for i, field := range fields {
        if i >= MaxConfigItems {
            break
        }
        validator.requiredFields[i] = field
        validator.fieldCount++
    }

    return validator // Array is copied by value
}

func (cv ConfigValidator) Validate(config map[string]string) []string {
    var missing []string

    // Array iteration - working with copy
    for i := 0; i < cv.fieldCount; i++ {
        field := cv.requiredFields[i]
        if _, exists := config[field]; !exists {
            missing = append(missing, field)
        }
    }

    return missing
}

// Demonstrate array comparison
func (cv ConfigValidator) Equals(other ConfigValidator) bool {
    return cv.requiredFields == other.requiredFields &&
           cv.fieldCount == other.fieldCount
}

func main() {
    // Create validators with different required fields
    webValidator := NewConfigValidator([]string{
        "server_port", "database_url", "api_key", "log_level",
    })

    dbValidator := NewConfigValidator([]string{
        "host", "port", "username", "password", "database",
    })

    // Test configurations
    webConfig := map[string]string{
        "server_port":  "8080",
        "database_url": "localhost:5432",
        "api_key":      "secret123",
        // missing log_level
    }

    dbConfig := map[string]string{
        "host":     "localhost",
        "port":     "5432",
        "username": "admin",
        "password": "secret",
        "database": "myapp",
    }

    // Validate configurations
    if missing := webValidator.Validate(webConfig); len(missing) > 0 {
        fmt.Printf("Web config missing: %v\n", missing)
    } else {
        fmt.Println("Web config is valid")
    }

    if missing := dbValidator.Validate(dbConfig); len(missing) > 0 {
        fmt.Printf("DB config missing: %v\n", missing)
    } else {
        fmt.Println("DB config is valid")
    }

    // Demonstrate array comparison
    webValidator2 := NewConfigValidator([]string{
        "server_port", "database_url", "api_key", "log_level",
    })

    fmt.Printf("Validators equal: %t\n", webValidator.Equals(webValidator2))
}
```

**Prerequisites**: Session 9

---

### Session 11: Slices Part 1: Declaration, Length, and Reference Types

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand slice internal structure and mechanics
- Master slice declaration and initialization patterns
- Learn the difference between length and capacity
- Apply slices effectively in automation data processing

**Videos Covered**:

- 3.5 Slices Part 1 (Declare and Length) (0:08:46)

**Key Concepts**:

- Slice header structure (pointer, length, capacity)
- nil slices vs empty slices
- Slice literals and make() function
- Length vs capacity concepts
- Reference semantics with slices

**Hands-on Exercise**:

```go
// Dynamic log processing with slices
package main

import (
    "fmt"
    "strings"
    "time"
)

type LogProcessor struct {
    entries []string
    errors  []string
}

func NewLogProcessor() *LogProcessor {
    return &LogProcessor{
        entries: make([]string, 0, 100), // Initial capacity of 100
        errors:  make([]string, 0, 10),  // Smaller capacity for errors
    }
}

func (lp *LogProcessor) AddEntry(entry string) {
    lp.entries = append(lp.entries, entry)

    // Check for error entries
    if strings.Contains(strings.ToLower(entry), "error") {
        lp.errors = append(lp.errors, entry)
    }
}

func (lp *LogProcessor) GetStats() (int, int, int, int) {
    return len(lp.entries), cap(lp.entries), len(lp.errors), cap(lp.errors)
}

func (lp *LogProcessor) GetRecentEntries(count int) []string {
    if count > len(lp.entries) {
        count = len(lp.entries)
    }

    start := len(lp.entries) - count
    return lp.entries[start:] // Slice operation
}

func main() {
    processor := NewLogProcessor()

    // Simulate log entries
    logEntries := []string{
        "INFO: Service started",
        "DEBUG: Processing request",
        "ERROR: Database connection failed",
        "INFO: Retrying connection",
        "ERROR: Authentication failed",
        "INFO: Service running normally",
    }

    fmt.Println("Processing log entries...")
    for i, entry := range logEntries {
        processor.AddEntry(fmt.Sprintf("[%s] %s",
            time.Now().Format("15:04:05"), entry))

        entryLen, entryCap, errorLen, errorCap := processor.GetStats()
        fmt.Printf("Entry %d: entries=%d/%d, errors=%d/%d\n",
            i+1, entryLen, entryCap, errorLen, errorCap)
    }

    // Get recent entries
    recent := processor.GetRecentEntries(3)
    fmt.Printf("\nRecent entries:\n")
    for _, entry := range recent {
        fmt.Printf("  %s\n", entry)
    }

    // Show all errors
    fmt.Printf("\nError entries:\n")
    for _, error := range processor.errors {
        fmt.Printf("  %s\n", error)
    }
}
```

**Prerequisites**: Session 10

---

### Session 12: Slices Part 2: Appending and Memory Management

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master slice growth and append mechanics
- Understand memory allocation patterns with slices
- Learn efficient slice management techniques
- Optimize slice usage for automation workloads

**Videos Covered**:

- 3.6 Slices Part 2 (Appending Slices) (0:15:32)

**Key Concepts**:

- append() function and slice growth
- Capacity doubling and memory allocation
- Slice reallocation and pointer invalidation
- Pre-allocation strategies for performance
- Memory efficiency with large slices

**Hands-on Exercise**:

```go
// Efficient batch processing with slice management
package main

import (
    "fmt"
    "runtime"
)

type BatchProcessor struct {
    items    []string
    batches  [][]string
    batchSize int
}

func NewBatchProcessor(batchSize int) *BatchProcessor {
    return &BatchProcessor{
        items:     make([]string, 0, batchSize*10), // Pre-allocate
        batches:   make([][]string, 0, 10),
        batchSize: batchSize,
    }
}

func (bp *BatchProcessor) AddItem(item string) {
    bp.items = append(bp.items, item)

    // Create batch when we reach batch size
    if len(bp.items) >= bp.batchSize {
        bp.createBatch()
    }
}

func (bp *BatchProcessor) createBatch() {
    if len(bp.items) == 0 {
        return
    }

    // Create new slice for batch (copy to avoid sharing)
    batch := make([]string, len(bp.items))
    copy(batch, bp.items)

    bp.batches = append(bp.batches, batch)

    // Reset items slice but keep capacity
    bp.items = bp.items[:0]
}

func (bp *BatchProcessor) ProcessBatches() {
    // Process any remaining items
    if len(bp.items) > 0 {
        bp.createBatch()
    }

    fmt.Printf("Processing %d batches...\n", len(bp.batches))

    for i, batch := range bp.batches {
        fmt.Printf("Batch %d: %d items\n", i+1, len(batch))

        // Simulate processing
        for j, item := range batch {
            if j < 3 { // Show first 3 items
                fmt.Printf("  Processing: %s\n", item)
            } else if j == 3 {
                fmt.Printf("  ... and %d more items\n", len(batch)-3)
                break
            }
        }
    }
}

func (bp *BatchProcessor) GetMemoryStats() {
    var m runtime.MemStats
    runtime.ReadMemStats(&m)

    fmt.Printf("Memory stats:\n")
    fmt.Printf("  Items slice - len: %d, cap: %d\n", len(bp.items), cap(bp.items))
    fmt.Printf("  Batches - count: %d, cap: %d\n", len(bp.batches), cap(bp.batches))
    fmt.Printf("  Heap alloc: %d KB\n", m.HeapAlloc/1024)
}

func main() {
    processor := NewBatchProcessor(5)

    // Add items to processor
    for i := 0; i < 23; i++ {
        item := fmt.Sprintf("automation-task-%03d", i)
        processor.AddItem(item)

        if i%5 == 4 { // Every 5 items
            processor.GetMemoryStats()
            fmt.Println()
        }
    }

    // Process all batches
    processor.ProcessBatches()

    // Final memory stats
    fmt.Println("\nFinal stats:")
    processor.GetMemoryStats()
}
```

**Prerequisites**: Session 11

---

### Session 13: Slices Part 3: Slicing Operations and References

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master slice operations and sub-slicing
- Understand slice sharing and reference behavior
- Learn safe slicing practices to avoid memory leaks
- Apply advanced slicing techniques in automation

**Videos Covered**:

- 3.7 Slices Part 3 (Taking Slices of Slices) (0:11:45)
- 3.8 Slices Part 4 (Slices and References) (0:05:51)

**Key Concepts**:

- Slice expressions and bounds checking
- Shared backing arrays and memory implications
- Copy vs slice operations
- Memory leaks with large slice references
- Safe slicing patterns

**Hands-on Exercise**:

```go
// Safe data windowing for time-series automation
package main

import (
    "fmt"
    "time"
)

type TimeSeriesData struct {
    timestamps []int64
    values     []float64
}

func NewTimeSeriesData(capacity int) *TimeSeriesData {
    return &TimeSeriesData{
        timestamps: make([]int64, 0, capacity),
        values:     make([]float64, 0, capacity),
    }
}

func (ts *TimeSeriesData) AddPoint(timestamp int64, value float64) {
    ts.timestamps = append(ts.timestamps, timestamp)
    ts.values = append(ts.values, value)
}

// Safe windowing - creates copy to avoid memory leaks
func (ts *TimeSeriesData) GetWindow(start, end int) *TimeSeriesData {
    if start < 0 || end > len(ts.timestamps) || start >= end {
        return NewTimeSeriesData(0)
    }

    window := NewTimeSeriesData(end - start)

    // Copy data instead of sharing slice
    for i := start; i < end; i++ {
        window.AddPoint(ts.timestamps[i], ts.values[i])
    }

    return window
}

// Unsafe windowing - shares backing array (for comparison)
func (ts *TimeSeriesData) GetWindowUnsafe(start, end int) *TimeSeriesData {
    if start < 0 || end > len(ts.timestamps) || start >= end {
        return NewTimeSeriesData(0)
    }

    return &TimeSeriesData{
        timestamps: ts.timestamps[start:end], // Shares backing array
        values:     ts.values[start:end],     // Shares backing array
    }
}

func (ts *TimeSeriesData) CalculateAverage() float64 {
    if len(ts.values) == 0 {
        return 0
    }

    var sum float64
    for _, value := range ts.values {
        sum += value
    }

    return sum / float64(len(ts.values))
}

func (ts *TimeSeriesData) Size() int {
    return len(ts.timestamps)
}

func main() {
    // Create large time series dataset
    data := NewTimeSeriesData(10000)

    // Add sample data points
    baseTime := time.Now().Unix()
    for i := 0; i < 1000; i++ {
        timestamp := baseTime + int64(i*60) // Every minute
        value := 50.0 + float64(i%100)     // Varying values
        data.AddPoint(timestamp, value)
    }

    fmt.Printf("Original dataset size: %d points\n", data.Size())

    // Get safe window (last 100 points)
    safeWindow := data.GetWindow(900, 1000)
    fmt.Printf("Safe window size: %d points\n", safeWindow.Size())
    fmt.Printf("Safe window average: %.2f\n", safeWindow.CalculateAverage())

    // Get unsafe window (for comparison)
    unsafeWindow := data.GetWindowUnsafe(900, 1000)
    fmt.Printf("Unsafe window size: %d points\n", unsafeWindow.Size())
    fmt.Printf("Unsafe window average: %.2f\n", unsafeWindow.CalculateAverage())

    // Demonstrate slice sharing issue
    fmt.Println("\nDemonstrating slice sharing:")

    // Modify original data
    data.timestamps[950] = 999999
    data.values[950] = -1.0

    fmt.Printf("After modifying original data:\n")
    fmt.Printf("Safe window average: %.2f (unchanged)\n", safeWindow.CalculateAverage())
    fmt.Printf("Unsafe window average: %.2f (changed!)\n", unsafeWindow.CalculateAverage())
}
```

**Prerequisites**: Session 12

---

### Session 14: Strings, Maps, and Range Mechanics

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master string operations and UTF-8 handling
- Understand map operations and performance characteristics
- Learn range mechanics for different data types
- Apply strings and maps in automation configuration

**Videos Covered**:

- 3.9 Slices Part 5 (Strings and Slices) (0:08:29)
- 3.10 Slices Part 6 (Range Mechanics) (0:04:35)
- 3.11 Maps (0:08:03)

**Key Concepts**:

- String immutability and UTF-8 encoding
- String to []byte conversions and performance
- Map declaration, initialization, and operations
- Map performance characteristics and hash collisions
- Range over strings, slices, arrays, and maps

**Hands-on Exercise**:

```go
// Configuration management with strings and maps
package main

import (
    "fmt"
    "strings"
    "unicode/utf8"
)

type ConfigManager struct {
    settings map[string]string
    metadata map[string]map[string]interface{}
}

func NewConfigManager() *ConfigManager {
    return &ConfigManager{
        settings: make(map[string]string),
        metadata: make(map[string]map[string]interface{}),
    }
}

func (cm *ConfigManager) SetConfig(key, value string) {
    cm.settings[key] = value

    // Store metadata about the configuration
    if cm.metadata[key] == nil {
        cm.metadata[key] = make(map[string]interface{})
    }

    cm.metadata[key]["length"] = utf8.RuneCountInString(value)
    cm.metadata[key]["bytes"] = len(value)
    cm.metadata[key]["type"] = cm.inferType(value)
}

func (cm *ConfigManager) inferType(value string) string {
    value = strings.TrimSpace(value)

    if value == "true" || value == "false" {
        return "boolean"
    }

    if strings.Contains(value, ".") {
        return "float"
    }

    // Check if it's a number
    for _, r := range value {
        if r < '0' || r > '9' {
            return "string"
        }
    }

    return "integer"
}

func (cm *ConfigManager) GetConfig(key string) (string, bool) {
    value, exists := cm.settings[key]
    return value, exists
}

func (cm *ConfigManager) ListConfigs() {
    fmt.Println("Configuration Settings:")

    // Range over map
    for key, value := range cm.settings {
        meta := cm.metadata[key]
        fmt.Printf("  %s = %s [type: %s, runes: %d, bytes: %d]\n",
            key, value, meta["type"], meta["length"], meta["bytes"])
    }
}

func (cm *ConfigManager) SearchConfigs(pattern string) []string {
    var matches []string

    pattern = strings.ToLower(pattern)

    for key, value := range cm.settings {
        // Search in key
        if strings.Contains(strings.ToLower(key), pattern) {
            matches = append(matches, key)
            continue
        }

        // Search in value
        if strings.Contains(strings.ToLower(value), pattern) {
            matches = append(matches, key)
        }
    }

    return matches
}

func (cm *ConfigManager) ProcessStringData(data string) map[string]int {
    wordCount := make(map[string]int)

    // Range over string (by rune)
    words := strings.Fields(data)
    for _, word := range words {
        // Clean word and count
        word = strings.ToLower(strings.Trim(word, ".,!?"))
        if word != "" {
            wordCount[word]++
        }
    }

    return wordCount
}

func main() {
    config := NewConfigManager()

    // Set various configuration values
    config.SetConfig("server_port", "8080")
    config.SetConfig("database_url", "postgresql://localhost:5432/mydb")
    config.SetConfig("enable_logging", "true")
    config.SetConfig("max_connections", "100")
    config.SetConfig("timeout_seconds", "30.5")
    config.SetConfig("service_name", "Automation Service 🚀")

    // List all configurations
    config.ListConfigs()

    // Search configurations
    fmt.Println("\nSearching for 'server':")
    matches := config.SearchConfigs("server")
    for _, match := range matches {
        if value, exists := config.GetConfig(match); exists {
            fmt.Printf("  Found: %s = %s\n", match, value)
        }
    }

    // Process text data
    logData := "Error connecting to database. Retrying connection. Connection successful."
    wordStats := config.ProcessStringData(logData)

    fmt.Println("\nWord frequency in log data:")
    for word, count := range wordStats {
        fmt.Printf("  '%s': %d\n", word, count)
    }
}
```

**Prerequisites**: Session 13
